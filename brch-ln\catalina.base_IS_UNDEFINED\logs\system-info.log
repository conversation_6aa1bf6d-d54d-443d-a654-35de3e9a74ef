09:58:26.459 [main] INFO  o.s.b.d.r.<PERSON>artApplicationListener - [onApplicationStartingEvent,88] - <PERSON><PERSON> disabled due to an agent-based reloader being active
09:58:26.857 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
09:58:27.136 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 28976 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
09:58:27.136 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln-xc
09:58:27.200 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
09:58:27.200 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
09:58:27.201 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
09:58:29.656 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:29.657 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:58:29.884 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 214 ms. Found 0 JPA repository interfaces.
09:58:29.907 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:29.908 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
09:58:30.059 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 150 ms. Found 0 MongoDB repository interfaces.
09:58:30.072 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:30.073 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
09:58:30.224 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 144 ms. Found 0 Redis repository interfaces.
09:58:30.729 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
09:58:30.793 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:30.813 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:30.867 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
09:58:30.868 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
09:58:30.871 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:30.887 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:30.890 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:30.927 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:31.113 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - [logMessage,136] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
09:58:49.812 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
09:58:50.177 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
09:58:50.450 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 16132 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
09:58:50.451 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
09:58:50.530 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
09:58:50.530 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
09:58:50.530 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
09:58:52.612 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:52.612 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
09:58:52.844 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 221 ms. Found 0 JPA repository interfaces.
09:58:52.862 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:52.864 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
09:58:53.014 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 150 ms. Found 0 MongoDB repository interfaces.
09:58:53.025 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
09:58:53.026 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
09:58:53.180 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 148 ms. Found 0 Redis repository interfaces.
09:58:53.654 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
09:58:53.714 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.732 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.777 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
09:58:53.778 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
09:58:53.780 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.797 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:53.989 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.033 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.056 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.091 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.183 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.187 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.193 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.201 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.208 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.209 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.217 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.222 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.225 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.229 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.243 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.325 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.378 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.399 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.415 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.420 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:54.901 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
09:58:55.144 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
09:58:55.193 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
09:58:55.356 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
09:58:55.466 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
09:58:55.686 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
09:58:55.703 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
09:58:55.721 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:55.745 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:55.751 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:55.756 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:55.776 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:55.777 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
09:58:56.284 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
09:58:56.304 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
09:58:56.304 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:58:56.304 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
09:58:56.433 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
09:58:56.433 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 5902 ms
09:58:56.678 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
09:58:56.722 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
09:58:57.114 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
09:58:57.303 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
09:58:58.671 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
09:58:58.766 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
09:58:58.789 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
09:59:01.230 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
09:59:01.777 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
09:59:09.638 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
09:59:13.471 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
09:59:14.635 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
09:59:14.984 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
09:59:15.094 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
09:59:16.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
09:59:16.290 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
09:59:16.291 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
09:59:16.307 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
09:59:16.946 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:59:17.754 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:59:18.311 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:59:18.882 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:59:18.950 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
09:59:19.004 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
09:59:19.052 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
09:59:19.069 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
09:59:19.078 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
09:59:19.097 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
09:59:19.111 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
09:59:19.116 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
09:59:19.120 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
09:59:19.125 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
09:59:19.129 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
09:59:19.134 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
09:59:19.140 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
09:59:19.145 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
09:59:19.151 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
09:59:19.158 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
09:59:19.170 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
09:59:19.855 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
09:59:19.881 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 30.084 seconds (JVM running for 36.76)
10:30:46.781 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
10:30:47.151 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
10:30:47.426 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 31952 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
10:30:47.427 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
10:30:47.495 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
10:30:47.495 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
10:30:47.495 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
10:30:49.715 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
10:30:49.715 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
10:30:49.956 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 230 ms. Found 0 JPA repository interfaces.
10:30:49.976 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
10:30:49.976 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
10:30:50.117 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 140 ms. Found 0 MongoDB repository interfaces.
10:30:50.129 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
10:30:50.129 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
10:30:50.296 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 160 ms. Found 0 Redis repository interfaces.
10:30:50.773 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:30:50.832 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:50.851 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:50.896 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
10:30:50.896 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
10:30:50.899 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:50.912 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:50.914 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:50.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.099 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.135 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.156 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.188 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.280 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.284 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.289 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.300 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.303 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.305 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.313 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.382 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.443 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.455 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.459 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:51.902 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
10:30:52.160 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
10:30:52.198 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
10:30:52.338 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
10:30:52.431 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
10:30:52.650 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
10:30:52.664 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
10:30:52.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:52.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:52.715 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:52.719 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:52.739 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:52.740 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
10:30:53.189 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
10:30:53.208 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
10:30:53.208 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:30:53.208 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
10:30:53.336 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
10:30:53.336 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 5840 ms
10:30:53.570 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:30:53.613 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:30:54.004 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
10:30:54.178 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
10:30:55.596 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
10:30:55.720 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
10:30:55.749 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
10:30:58.221 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
10:30:58.768 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
10:31:06.807 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
10:31:08.984 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
10:31:09.648 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
10:31:09.866 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
10:31:09.909 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
10:31:10.806 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
10:31:10.829 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
10:31:10.831 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
10:31:10.847 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
10:31:11.230 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
10:31:11.730 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
10:31:12.027 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
10:31:12.297 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
10:31:12.327 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
10:31:12.351 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
10:31:12.372 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
10:31:12.385 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
10:31:12.389 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
10:31:12.401 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
10:31:12.409 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
10:31:12.411 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
10:31:12.414 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
10:31:12.418 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
10:31:12.420 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
10:31:12.422 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
10:31:12.424 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
10:31:12.444 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
10:31:12.447 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
10:31:12.450 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
10:31:12.453 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
10:31:12.757 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
10:31:12.775 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 26.008 seconds (JVM running for 29.349)
10:33:50.351 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:33:50.351 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
10:33:50.354 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 2 ms
10:33:53.601 [http-nio-8080-exec-2] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-01 10:33:53.577","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508014015633578","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
10:33:54.606 [http-nio-8080-exec-6] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数101
10:37:51.450 [http-nio-8080-exec-14] INFO  c.s.m.b.c.c.ContractSyncController - [syncContracts,30] - 开始执行合同同步任务
10:38:21.948 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
10:38:22.088 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
23:11:54.519 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
23:11:54.922 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
23:11:55.201 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 40976 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
23:11:55.201 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
23:11:55.272 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
23:11:55.272 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
23:11:55.272 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
23:11:57.665 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:11:57.666 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
23:11:57.897 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 218 ms. Found 0 JPA repository interfaces.
23:11:57.927 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:11:57.927 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
23:11:58.055 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 128 ms. Found 0 MongoDB repository interfaces.
23:11:58.069 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:11:58.069 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
23:11:58.201 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 124 ms. Found 0 Redis repository interfaces.
23:11:58.694 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:11:58.762 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:58.793 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:58.854 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
23:11:58.854 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
23:11:58.857 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:58.875 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:58.878 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:58.908 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.205 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.249 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.271 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.306 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.389 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.395 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.399 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.405 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.410 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.418 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.431 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.433 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.436 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.445 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.449 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.531 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.588 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.604 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:11:59.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:00.726 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
23:12:00.955 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:12:01.004 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
23:12:01.172 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
23:12:01.285 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
23:12:01.529 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
23:12:01.544 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
23:12:01.562 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:01.571 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:01.579 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:01.582 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:01.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:01.602 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:12:02.070 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
23:12:02.088 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
23:12:02.089 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:12:02.089 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
23:12:02.213 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
23:12:02.213 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 6940 ms
23:12:02.456 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
23:12:02.501 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
23:12:02.882 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
23:12:03.079 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
23:12:04.481 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
23:12:04.580 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
23:12:04.605 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:12:07.108 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
23:12:07.665 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
23:12:15.369 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:12:17.309 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
23:12:17.940 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
23:12:18.127 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
23:12:18.179 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
23:12:19.019 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
23:12:19.041 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
23:12:19.043 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
23:12:19.074 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
23:12:19.403 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:12:19.859 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:12:20.130 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:12:20.397 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:12:20.431 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
23:12:20.454 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
23:12:20.470 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
23:12:20.479 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
23:12:20.482 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
23:12:20.490 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
23:12:20.496 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
23:12:20.497 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
23:12:20.499 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
23:12:20.501 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
23:12:20.502 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
23:12:20.504 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
23:12:20.506 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
23:12:20.507 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
23:12:20.510 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
23:12:20.513 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
23:12:20.517 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
23:12:20.834 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:12:20.851 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 26.349 seconds (JVM running for 29.907)
23:12:23.966 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
23:12:23.986 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
23:14:24.082 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
23:14:24.571 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
23:14:24.901 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 13668 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
23:14:24.901 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
23:14:24.968 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
23:14:24.968 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
23:14:24.968 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
23:14:27.395 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:14:27.396 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
23:14:27.725 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 315 ms. Found 0 JPA repository interfaces.
23:14:27.750 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:14:27.750 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
23:14:27.912 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 162 ms. Found 0 MongoDB repository interfaces.
23:14:27.931 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:14:27.933 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
23:14:28.099 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 155 ms. Found 0 Redis repository interfaces.
23:14:28.663 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:14:28.737 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:28.758 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:28.813 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
23:14:28.814 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
23:14:28.816 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:28.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:28.834 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:28.863 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.089 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.115 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.250 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.256 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.262 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.271 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.279 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.284 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.287 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.301 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.381 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.468 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:29.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:30.707 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
23:14:31.028 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:14:31.067 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
23:14:31.209 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
23:14:31.301 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
23:14:31.514 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
23:14:31.528 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
23:14:31.566 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:31.585 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:31.596 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:31.602 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:31.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:31.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:14:32.120 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
23:14:32.141 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
23:14:32.141 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:14:32.142 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
23:14:32.279 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
23:14:32.279 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 7309 ms
23:14:32.533 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
23:14:32.576 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
23:14:32.958 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
23:14:33.152 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
23:14:34.640 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
23:14:34.758 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
23:14:34.784 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:14:37.867 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
23:14:38.544 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
23:14:47.091 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:14:49.402 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
23:14:50.102 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
23:14:50.321 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
23:14:50.363 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
23:14:51.259 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
23:14:51.282 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
23:14:51.284 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
23:14:51.297 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
23:14:51.699 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:14:52.266 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:14:52.693 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:14:53.069 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:14:53.135 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
23:14:53.176 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
23:14:53.201 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
23:14:53.212 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
23:14:53.216 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
23:14:53.225 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
23:14:53.231 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
23:14:53.233 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
23:14:53.235 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
23:14:53.236 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
23:14:53.238 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
23:14:53.240 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
23:14:53.242 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
23:14:53.244 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
23:14:53.247 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
23:14:53.250 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
23:14:53.254 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
23:14:53.596 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:14:53.634 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 29.571 seconds (JVM running for 33.467)
23:15:08.486 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:15:08.486 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
23:15:08.489 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 3 ms
23:15:13.749 [http-nio-8080-exec-3] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-01 23:15:13.716","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508014061313717","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
23:15:15.712 [http-nio-8080-exec-6] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数101
23:15:27.419 [http-nio-8080-exec-12] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-01 23:15:27.419","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508014061327419","loginStatus":"1","authType":"11","account":"sys","userCode":"sys"}
23:15:28.107 [http-nio-8080-exec-15] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数163
23:15:39.408 [http-nio-8080-exec-24] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:15:39.824 [http-nio-8080-exec-24] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:16:33.277 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
23:16:33.285 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 8 ms. Found 0 MongoDB repository interfaces.
23:16:33.296 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
23:16:33.299 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
23:16:33.299 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
23:16:33.303 [http-nio-8080-exec-26] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
23:16:34.282 [http-nio-8080-exec-26] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:16:34.294 [http-nio-8080-exec-26] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
23:16:34.308 [http-nio-8080-exec-26] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
23:16:54.332 [http-nio-8080-exec-35] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:16:54.433 [http-nio-8080-exec-35] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:18:06.110 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
23:18:06.240 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
23:18:13.948 [main] INFO  o.s.b.d.r.RestartApplicationListener - [onApplicationStartingEvent,88] - Restart disabled due to an agent-based reloader being active
23:18:14.355 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.1.7.Final
23:18:14.621 [main] INFO  com.sccl.Application - [logStarting,55] - Starting Application using Java 1.8.0_421 on WIN-TVKN4A60TS0 with PID 8872 (E:\cl-project\ln-nenghao\brch-ln\sccl-web\target\classes started by Administrator in E:\cl-project\ln-nenghao\brch-ln)
23:18:14.621 [main] INFO  com.sccl.Application - [logStartupProfileInfo,668] - The following profiles are active: dev-ln
23:18:14.685 [main] INFO  o.s.b.d.r.ChangeableUrls - [logTo,255] - The Class-Path manifest attribute in E:\.m2\repository\com\sun\xml\ws\jaxws-tools\2.2.6\jaxws-tools-2.2.6.jar referenced one or more files that do not exist: file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxws-rt.jar,file:/E:/.m2/repository/com/sun/xml/ws/jaxws-tools/2.2.6/jaxb-xjc.jar
23:18:14.685 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
23:18:14.685 [main] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - [logTo,255] - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
23:18:17.158 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:18:17.159 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
23:18:17.389 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 217 ms. Found 0 JPA repository interfaces.
23:18:17.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:18:17.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
23:18:17.536 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 127 ms. Found 0 MongoDB repository interfaces.
23:18:17.550 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [multipleStoresDetected,250] - Multiple Spring Data modules found, entering strict repository configuration mode!
23:18:17.550 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,128] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
23:18:17.688 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - [registerRepositoriesIn,188] - Finished Spring Data repository scanning in 130 ms. Found 0 Redis repository interfaces.
23:18:18.175 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
23:18:18.241 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'shiroConfig' of type [com.sccl.config.ShiroConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.261 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'getEhCacheManager' of type [org.apache.shiro.cache.ehcache.EhCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.319 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,158] - Cache with name 'com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache' does not yet exist.  Creating now.
23:18:18.319 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,165] - Added EhCache named [com.sccl.common.shiro.jwt.realm.UserRealm.authorizationCache]
23:18:18.322 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'userRealm' of type [com.sccl.common.shiro.jwt.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.338 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.340 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'captchaValidateFilter' of type [com.sccl.common.shiro.web.filter.captcha.CaptchaValidateFilter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.365 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'druidMutilConfig' of type [com.sccl.framework.config.DruidMutilConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'bsConfig' of type [com.sccl.framework.config.BsConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.625 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'ecmDsProps' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.661 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dataSourceProperties' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceWrapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.743 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'dynamicDataSource' of type [com.sccl.framework.datasource.DynamicDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.752 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.758 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.763 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration' of type [org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties' of type [org.springframework.boot.autoconfigure.transaction.TransactionProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.771 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'platformTransactionManagerCustomizers' of type [org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.780 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.784 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.789 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.799 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.802 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.883 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.969 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:18.975 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.023 [main] INFO  c.a.d.p.DruidDataSource - [init,983] - {dataSource-1} inited
23:18:20.254 [main] INFO  o.h.j.i.u.LogHelper - [logPersistenceUnitInformation,31] - HHH000204: Processing PersistenceUnitInfo [name: default]
23:18:20.305 [main] INFO  o.hibernate.Version - [logVersion,44] - HHH000412: Hibernate ORM core version 5.4.32.Final
23:18:20.478 [main] INFO  o.h.a.common.Version - [<clinit>,56] - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
23:18:20.589 [main] INFO  o.h.dialect.Dialect - [<init>,175] - HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
23:18:20.826 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - [initiateService,52] - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
23:18:20.841 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [buildNativeEntityManagerFactory,437] - Initialized JPA EntityManagerFactory for persistence unit 'default'
23:18:20.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.889 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'transactionManager' of type [org.springframework.orm.jpa.JpaTransactionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.893 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceInterceptor' of type [com.sccl.framework.common.transaction.TxAdviceInterceptor$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.912 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdvice' of type [org.springframework.transaction.interceptor.TransactionInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:20.913 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - [postProcessAfterInitialization,376] - Bean 'txAdviceAdvisor' of type [org.springframework.aop.support.DefaultPointcutAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
23:18:21.376 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [initialize,108] - Tomcat initialized with port(s): 8080 (http)
23:18:21.396 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
23:18:21.396 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:18:21.396 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.55]
23:18:21.525 [main] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring embedded WebApplicationContext
23:18:21.525 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - [prepareWebApplicationContext,289] - Root WebApplicationContext: initialization completed in 6839 ms
23:18:21.761 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
23:18:21.802 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
23:18:22.167 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [notifyKieModuleFound,152] - Found kmodule: file:/E:/cl-project/ln-nenghao/brch-ln/sccl-web/target/classes/META-INF/kmodule.xml
23:18:22.358 [main] INFO  o.d.c.k.b.i.ClasspathKieProject - [generatePomPropertiesFromPom,376] - Recursed up folders, found and used pom.xml E:\cl-project\ln-nenghao\brch-ln\sccl-web\pom.xml
23:18:23.714 [main] INFO  c.s.m.b.s.u.StationAuditUtil - [<clinit>,76] - accountService getBean is null
23:18:23.810 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-protocol.yml
23:18:23.836 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:18:26.200 [main] INFO  o.a.s.c.e.EhCacheManager - [getCache,169] - Using existing EHCache named [loginRecordCache]
23:18:26.714 [main] INFO  c.s.m.s.a.c.LocalCacheConfig - [localCacheManager,48] - 本地缓存注入完成，开启权限缓存，本地缓存长度：1000，过期时间：1800000min，策略：only_local
23:18:34.390 [main] INFO  c.s.t.f.u.i.PropertiesHolder - [load,80] - 加载资源文件：timing-config-sta.yml
23:18:36.454 [main] INFO  o.s.b.f.a.AutowiredAnnotationBeanPostProcessor - [lambda$buildAutowiringMetadata$1,478] - Autowired annotation is not supported on static fields: private static org.springframework.web.client.RestTemplate com.sccl.modules.util.WorkflowUtil.restTemplate
23:18:37.091 [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - [initHandlerMethods,69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
23:18:37.288 [main] INFO  o.s.b.d.a.OptionalLiveReloadServer - [startServer,58] - LiveReload server is running on port 35729
23:18:37.330 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - [<init>,53] - Adding welcome page: class path resource [META-INF/resources/index.html]
23:18:38.179 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
23:18:38.201 [main] INFO  o.s.b.w.e.t.TomcatWebServer - [start,220] - Tomcat started on port(s): 8080 (http) with context path '/energy-cost'
23:18:38.202 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,147] - Context refreshed
23:18:38.217 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,150] - Found 5 custom documentation plugin(s)
23:18:38.538 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:18:39.001 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:18:39.274 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:18:39.551 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:18:39.581 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_1
23:18:39.607 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingGET_1
23:18:39.623 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addSaveUsingPOST_1
23:18:39.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingGET_2
23:18:39.635 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editSaveUsingPOST_1
23:18:39.642 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getUserInfoUsingGET_1
23:18:39.646 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
23:18:39.649 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingHEAD_1
23:18:39.650 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPOST_1
23:18:39.652 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPUT_1
23:18:39.654 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingPATCH_1
23:18:39.656 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingDELETE_1
23:18:39.657 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingOPTIONS_1
23:18:39.659 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingTRACE_1
23:18:39.662 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingPOST_1
23:18:39.664 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingGET_1
23:18:39.667 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: resetPwdUsingPOST_1
23:18:39.976 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
23:18:39.995 [main] INFO  com.sccl.Application - [logStarted,61] - Started Application in 26.062 seconds (JVM running for 29.804)
23:19:15.255 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/energy-cost] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:19:15.256 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,525] - Initializing Servlet 'dispatcherServlet'
23:19:15.259 [http-nio-8080-exec-1] INFO  o.s.w.s.DispatcherServlet - [initServletBean,547] - Completed initialization in 3 ms
23:19:44.022 [http-nio-8080-exec-3] INFO  c.s.m.u.w.s.WfProcInstServiceImpl - [startProcessInst,220] - processLog 启流程成功 busi_id:4711985359356043264 参数:{"areaCode":"0000","busiAlias":"ADD_AMM","busiCode":"","busiDeptCode":"**********","busiDeptName":"辽宁－辽宁电信","busiId":"4711985359356043264","busiTitle":"新增电表(1路1号电表测试1)审批","busiType":"BusiType_1","callCount":0,"deptCode":"**********","deptName":"辽宁－辽宁电信","loginId":"sys","procDefKey":"process20190522103036821_3895465866464202752","shardKey":"0000","userId":2,"userName":"自动任务","variables":{"busiId":"4711985359356043264","busiAlias":"ADD_AMM","busiTitle":"新增电表(1路1号电表测试1)审批","creatorLoginId":"sys","creatorId":2,"shardKey":"0000","areaCode":"0000","busiDeptCode":"**********","busiDeptName":"辽宁－辽宁电信","deptCode":"**********","deptName":"辽宁－辽宁电信","procDefKey":"process20190522103036821_3895465866464202752","PROP_IS_AUTO_DO":"0","isFitstNodeAutoSubmit":"false","appointUserId":"sys"}} 返回:{"success":true,"procInstId":"4715743139921543168","wfModel":"{\"callCount\":0,\"mqRoutingKey\":\"createTask\",\"variables\":{},\"wfTask\":{\"areaCode\":\"0000\",\"busiAlias\":\"ADD_AMM\",\"busiDeptCode\":\"**********\",\"busiDeptName\":\"辽宁－辽宁电信\",\"busiId\":\"4711985359356043264\",\"busiType\":\"BusiType_1\",\"customProps\":{\"deptName\":\"辽宁－辽宁电信\",\"PROP_IS_AUTO_DO\":\"0\",\"isFitstNodeAutoSubmit\":\"false\",\"busiId\":\"4711985359356043264\",\"busiDeptCode\":\"**********\",\"creatorId\":\"2\",\"busiTitle\":\"新增电表(1路1号电表测试1)审批\",\"shardKey\":\"0000\",\"creatorLoginId\":\"sys\",\"areaCode\":\"0000\",\"busiDeptName\":\"辽宁－辽宁电信\",\"busiAlias\":\"ADD_AMM\",\"deptCode\":\"**********\",\"procDefKey\":\"process20190522103036821_3895465866464202752\",\"appointUserId\":\"sys\"},\"eventType\":\"00\",\"genTime\":*************,\"id\":4715743140479385600,\"nodeId\":\"UserTask_1832\",\"nodeName\":\"电表新增\",\"outDays\":0.0,\"procDefId\":\"3910691324302594048\",\"procInstId\":\"4715743139921543168\",\"procModelId\":\"3895466858597453824\",\"procTaskId\":\"4715743140479385600\",\"retry\":0,\"shardKey\":\"0000\",\"status\":\"1\",\"subEventType\":\"0\",\"taskName\":\"新增电表(1路1号电表测试1)审批[电表新增]\"}}"}
23:19:44.336 [http-nio-8080-exec-3] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:19:44.342 [http-nio-8080-exec-3] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁－辽宁电信]
23:19:44.342 [http-nio-8080-exec-3] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:20:08.571 [http-nio-8080-exec-9] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-01 23:20:08.542","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508014061608543","loginStatus":"1","authType":"11","account":"sys","userCode":"sys"}
23:20:09.681 [http-nio-8080-exec-12] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数163
23:20:12.993 [http-nio-8080-exec-21] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:20:13.097 [http-nio-8080-exec-21] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:20:17.175 [http-nio-8080-exec-24] INFO  c.s.m.u.w.s.WfProcInstServiceImpl - [startProcessInst,220] - processLog 启流程成功 busi_id:4711985359356043264 参数:{"areaCode":"0000","busiAlias":"ADD_AMM","busiCode":"","busiDeptCode":"**********","busiDeptName":"辽宁－辽宁电信","busiId":"4711985359356043264","busiTitle":"新增电表(1路1号电表测试1)审批","busiType":"BusiType_1","callCount":0,"deptCode":"**********","deptName":"辽宁－辽宁电信","loginId":"sys","procDefKey":"process20190522103036821_3895465866464202752","shardKey":"0000","userId":2,"userName":"自动任务","variables":{"busiId":"4711985359356043264","busiAlias":"ADD_AMM","busiTitle":"新增电表(1路1号电表测试1)审批","creatorLoginId":"sys","creatorId":2,"shardKey":"0000","areaCode":"0000","busiDeptCode":"**********","busiDeptName":"辽宁－辽宁电信","deptCode":"**********","deptName":"辽宁－辽宁电信","procDefKey":"process20190522103036821_3895465866464202752","PROP_IS_AUTO_DO":"0","isFitstNodeAutoSubmit":"false","appointUserId":"sys"}} 返回:{"success":true,"procInstId":"4715743299196043264","wfModel":"{\"callCount\":0,\"mqRoutingKey\":\"createTask\",\"variables\":{},\"wfTask\":{\"areaCode\":\"0000\",\"busiAlias\":\"ADD_AMM\",\"busiDeptCode\":\"**********\",\"busiDeptName\":\"辽宁－辽宁电信\",\"busiId\":\"4711985359356043264\",\"busiType\":\"BusiType_1\",\"customProps\":{\"deptName\":\"辽宁－辽宁电信\",\"PROP_IS_AUTO_DO\":\"0\",\"isFitstNodeAutoSubmit\":\"false\",\"busiId\":\"4711985359356043264\",\"busiDeptCode\":\"**********\",\"creatorId\":\"2\",\"busiTitle\":\"新增电表(1路1号电表测试1)审批\",\"shardKey\":\"0000\",\"creatorLoginId\":\"sys\",\"areaCode\":\"0000\",\"busiDeptName\":\"辽宁－辽宁电信\",\"busiAlias\":\"ADD_AMM\",\"deptCode\":\"**********\",\"procDefKey\":\"process20190522103036821_3895465866464202752\",\"appointUserId\":\"sys\"},\"eventType\":\"00\",\"genTime\":*************,\"id\":4715743299208626177,\"nodeId\":\"UserTask_1832\",\"nodeName\":\"电表新增\",\"outDays\":0.0,\"procDefId\":\"3910691324302594048\",\"procInstId\":\"4715743299196043264\",\"procModelId\":\"3895466858597453824\",\"procTaskId\":\"4715743299208626177\",\"retry\":0,\"shardKey\":\"0000\",\"status\":\"1\",\"subEventType\":\"0\",\"taskName\":\"新增电表(1路1号电表测试1)审批[电表新增]\"}}"}
23:20:17.297 [http-nio-8080-exec-24] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:20:17.297 [http-nio-8080-exec-24] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁－辽宁电信]
23:20:17.298 [http-nio-8080-exec-24] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:20:37.872 [http-nio-8080-exec-30] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:20:37.937 [http-nio-8080-exec-30] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:24:26.779 [http-nio-8080-exec-50] INFO  c.s.c.hlog.HLogUtils - [writeLoginLog,121] - {"server":"sysPA","sysCode":"S000AN2021012048","remoteIp":"127.0.0.1","loginTime":"2025-08-01 23:24:26.778","logAppId":"QX661449_scnh_NHF_sys_PA","serialNbr":"202508014061866779","loginStatus":"1","authType":"11","account":"libingran","userCode":"libingran"}
23:24:27.906 [http-nio-8080-exec-54] INFO  c.s.c.s.j.r.UserRealm - [doGetAuthorizationInfo,102] - 调用鉴权方法，获取到的权限条数101
23:25:45.678 [http-nio-8080-exec-62] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:25:47.120 [http-nio-8080-exec-62] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:26:06.489 [http-nio-8080-exec-71] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:26:07.157 [http-nio-8080-exec-71] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:26:09.563 [http-nio-8080-exec-72] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,111] - 协议管理查询
23:26:10.217 [http-nio-8080-exec-72] INFO  c.s.m.b.a.c.AmmeterorprotocolController - [list,119] - 协议管理查询结束
23:26:28.414 [http-nio-8080-exec-15] INFO  c.s.m.u.w.s.WfProcInstServiceImpl - [startProcessInst,220] - processLog 启流程成功 busi_id:4191367718261137369 参数:{"areaCode":"0000","busiAlias":"MODIFY_AMM","busiCode":"","busiDeptCode":"**********","busiDeptName":"辽宁-鞍山分公司","busiId":"4191367718261137369","busiTitle":"修改电表(景子街购物)审批","busiType":"BusiType_1","callCount":0,"deptCode":"**********","deptName":"鞍山分公司现场综合维护中心","loginId":"libingran","procDefKey":"process20190523171548079_3895930223136477184","shardKey":"0000","userId":**********,"userName":"李冰然","variables":{"busiId":"4191367718261137369","busiAlias":"MODIFY_AMM","busiTitle":"修改电表(景子街购物)审批","creatorLoginId":"libingran","creatorId":**********,"shardKey":"0000","areaCode":"0000","busiDeptCode":"**********","busiDeptName":"辽宁-鞍山分公司","deptCode":"**********","deptName":"鞍山分公司现场综合维护中心","procDefKey":"process20190523171548079_3895930223136477184","PROP_IS_AUTO_DO":"0","isFitstNodeAutoSubmit":"false","appointUserId":"libingran"}} 返回:{"success":true,"procInstId":"4715744857161875456","wfModel":"{\"callCount\":0,\"mqRoutingKey\":\"createTask\",\"variables\":{},\"wfTask\":{\"areaCode\":\"0000\",\"busiAlias\":\"MODIFY_AMM\",\"busiDeptCode\":\"**********\",\"busiDeptName\":\"辽宁-鞍山分公司\",\"busiId\":\"4191367718261137369\",\"busiType\":\"BusiType_1\",\"customProps\":{\"deptName\":\"鞍山分公司现场综合维护中心\",\"PROP_IS_AUTO_DO\":\"0\",\"isFitstNodeAutoSubmit\":\"false\",\"busiId\":\"4191367718261137369\",\"busiDeptCode\":\"**********\",\"creatorId\":\"**********\",\"busiTitle\":\"修改电表(景子街购物)审批\",\"shardKey\":\"0000\",\"creatorLoginId\":\"libingran\",\"areaCode\":\"0000\",\"busiDeptName\":\"辽宁-鞍山分公司\",\"busiAlias\":\"MODIFY_AMM\",\"deptCode\":\"**********\",\"procDefKey\":\"process20190523171548079_3895930223136477184\",\"appointUserId\":\"libingran\"},\"eventType\":\"00\",\"genTime\":*************,\"id\":4715744857170264065,\"nodeId\":\"UserTask_6522\",\"nodeName\":\"电表修改\",\"outDays\":0.0,\"procDefId\":\"3910691230408904705\",\"procInstId\":\"4715744857161875456\",\"procModelId\":\"3895930290203398144\",\"procTaskId\":\"4715744857170264065\",\"retry\":0,\"shardKey\":\"0000\",\"status\":\"1\",\"subEventType\":\"0\",\"taskName\":\"修改电表(景子街购物)审批[电表修改]\"}}"}
23:26:28.555 [http-nio-8080-exec-15] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:26:28.556 [http-nio-8080-exec-15] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁-鞍山分公司]
23:26:28.561 [http-nio-8080-exec-15] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:38:11.804 [http-nio-8080-exec-46] INFO  c.s.m.u.w.s.WfProcInstServiceImpl - [startProcessInst,220] - processLog 启流程成功 busi_id:4191367718261137369 参数:{"areaCode":"0000","busiAlias":"MODIFY_AMM","busiCode":"","busiDeptCode":"**********","busiDeptName":"辽宁-鞍山分公司","busiId":"4191367718261137369","busiTitle":"修改电表(景子街购物)审批","busiType":"BusiType_1","callCount":0,"deptCode":"**********","deptName":"鞍山分公司现场综合维护中心","loginId":"libingran","procDefKey":"process20190523171548079_3895930223136477184","shardKey":"0000","userId":**********,"userName":"李冰然","variables":{"busiId":"4191367718261137369","busiAlias":"MODIFY_AMM","busiTitle":"修改电表(景子街购物)审批","creatorLoginId":"libingran","creatorId":**********,"shardKey":"0000","areaCode":"0000","busiDeptCode":"**********","busiDeptName":"辽宁-鞍山分公司","deptCode":"**********","deptName":"鞍山分公司现场综合维护中心","procDefKey":"process20190523171548079_3895930223136477184","PROP_IS_AUTO_DO":"0","isFitstNodeAutoSubmit":"false","appointUserId":"libingran"}} 返回:{"success":true,"procInstId":"4715747807082987520","wfModel":"{\"callCount\":0,\"mqRoutingKey\":\"createTask\",\"variables\":{},\"wfTask\":{\"areaCode\":\"0000\",\"busiAlias\":\"MODIFY_AMM\",\"busiDeptCode\":\"**********\",\"busiDeptName\":\"辽宁-鞍山分公司\",\"busiId\":\"4191367718261137369\",\"busiType\":\"BusiType_1\",\"customProps\":{\"deptName\":\"鞍山分公司现场综合维护中心\",\"PROP_IS_AUTO_DO\":\"0\",\"isFitstNodeAutoSubmit\":\"false\",\"busiId\":\"4191367718261137369\",\"busiDeptCode\":\"**********\",\"creatorId\":\"**********\",\"busiTitle\":\"修改电表(景子街购物)审批\",\"shardKey\":\"0000\",\"creatorLoginId\":\"libingran\",\"areaCode\":\"0000\",\"busiDeptName\":\"辽宁-鞍山分公司\",\"busiAlias\":\"MODIFY_AMM\",\"deptCode\":\"**********\",\"procDefKey\":\"process20190523171548079_3895930223136477184\",\"appointUserId\":\"libingran\"},\"eventType\":\"00\",\"genTime\":*************,\"id\":4715747807112347649,\"nodeId\":\"UserTask_6522\",\"nodeName\":\"电表修改\",\"outDays\":0.0,\"procDefId\":\"3910691230408904705\",\"procInstId\":\"4715747807082987520\",\"procModelId\":\"3895930290203398144\",\"procTaskId\":\"4715747807112347649\",\"retry\":0,\"shardKey\":\"0000\",\"status\":\"1\",\"subEventType\":\"0\",\"taskName\":\"修改电表(景子街购物)审批[电表修改]\"}}"}
23:38:11.930 [http-nio-8080-exec-46] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:38:11.930 [http-nio-8080-exec-46] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁-鞍山分公司]
23:38:11.931 [http-nio-8080-exec-46] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:38:25.259 [http-nio-8080-exec-50] INFO  c.s.m.u.w.s.WfTaskHisServiceImpl - [moveTaskToHis,38] - 迁移待办到已办表中
23:38:31.179 [http-nio-8080-exec-50] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:38:31.180 [http-nio-8080-exec-50] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁-鞍山分公司]
23:38:31.180 [http-nio-8080-exec-50] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:38:31.468 [http-nio-8080-exec-50] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [executeCompleteTask,846] - processLog 发送OA成功busi_id:4191367718261137369 
23:39:19.793 [http-nio-8080-exec-66] INFO  c.s.m.u.w.s.WfTaskHisServiceImpl - [moveTaskToHis,38] - 迁移待办到已办表中
23:39:21.158 [http-nio-8080-exec-66] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,188] - wfTask.getStatus().toString()1
23:39:21.158 [http-nio-8080-exec-66] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,189] - user.getCompanies()[**********,辽宁-鞍山分公司]
23:39:21.158 [http-nio-8080-exec-66] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [createTask,201] - wfTask.getStatus().toString()1
23:39:21.282 [http-nio-8080-exec-66] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [executeCompleteTask,846] - processLog 发送OA成功busi_id:4191367718261137369 
23:39:43.581 [http-nio-8080-exec-78] INFO  c.s.m.u.w.s.WfTaskHisServiceImpl - [moveTaskToHis,38] - 迁移待办到已办表中
23:39:45.402 [http-nio-8080-exec-78] INFO  c.s.m.u.w.s.WfTaskServiceImpl - [executeCompleteTask,846] - processLog 发送OA成功busi_id:4191367718261137369 
23:39:46.369 [http-nio-8080-exec-78] INFO  c.s.m.b.s.u.StationAuditUtil - [checkStationAudit,348] - 稽核流程开启,电表编号为：3972276672325107712
23:39:46.498 [http-nio-8080-exec-78] INFO  c.s.m.b.s.u.StationAuditUtil - [checkStationAudit,397] - 获取局站的对应编码
23:46:09.206 [SpringContextShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - [destroy,651] - Closing JPA EntityManagerFactory for persistence unit 'default'
23:46:09.345 [SpringContextShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2024] - {dataSource-0} closing ...
